"use client";

import { useRootContext } from '@/root-context';
import { Button } from '@/components/ui/button';
import { RefreshCw, Settings } from 'lucide-react';

export function AppConfigDisplay() {
  const { appConfig } = useRootContext();

  if (!appConfig) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Settings className="w-5 h-5" />
            App Configuration
          </h2>
        </div>
        <div className="text-center py-8 text-gray-500">
          Loading configuration...
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold flex items-center gap-2">
          <Settings className="w-5 h-5" />
          App Configuration
        </h2>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <div className="border rounded-lg p-4 space-y-2">
          <h3 className="font-medium text-green-700">Deposit Settings</h3>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span>Minimum Amount:</span>
              <span className="font-medium">{appConfig.minDepositAmount} TON</span>
            </div>
            <div className="flex justify-between">
              <span>Deposit Fee:</span>
              <span className="font-medium">{appConfig.depositFee} TON</span>
            </div>
            {appConfig.maxDepositAmount && (
              <div className="flex justify-between">
                <span>Maximum Amount:</span>
                <span className="font-medium">{appConfig.maxDepositAmount} TON</span>
              </div>
            )}
          </div>
        </div>

        <div className="border rounded-lg p-4 space-y-2">
          <h3 className="font-medium text-red-700">Withdrawal Settings</h3>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span>Withdrawal Fee:</span>
              <span className="font-medium">{appConfig.withdrawalFee} TON</span>
            </div>
          </div>
        </div>

        {appConfig.maintenanceMode && (
          <div className="border border-yellow-300 bg-yellow-50 rounded-lg p-4 md:col-span-2">
            <h3 className="font-medium text-yellow-800">Maintenance Mode</h3>
            <p className="text-sm text-yellow-700">
              The application is currently in maintenance mode.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
