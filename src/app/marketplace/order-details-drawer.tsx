"use client";

import { useState, useEffect } from 'react';
import { Drawer } from 'vaul';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, User, ShoppingCart, Package } from 'lucide-react';
import { useRootContext } from '@/root-context';
import { OrderEntity, UserEntity } from '@/core.constants';
import { getUserById } from '@/api/auth-api';
import { toast } from 'sonner';
import { httpsCallable } from 'firebase/functions';
import { firebaseFunctions } from '@/root-context';
import Image from 'next/image';

interface OrderDetailsDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: OrderEntity | null;
  orderType: 'seller' | 'buyer'; // Which tab we're on
  onOrderAction?: () => void; // Callback after successful action
}

export function OrderDetailsDrawer({ 
  open, 
  onOpenChange, 
  order, 
  orderType,
  onOrderAction 
}: OrderDetailsDrawerProps) {
  const { collections } = useRootContext();
  const [userInfo, setUserInfo] = useState<UserEntity | null>(null);
  const [loading, setLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [imageError, setImageError] = useState(false);

  const collection = order ? collections.find(c => c.id === order.collectionId) : null;
  
  // Determine which user to fetch based on order type
  const userIdToFetch = orderType === 'seller' ? order?.buyerId : order?.sellerId;

  useEffect(() => {
    if (open && userIdToFetch) {
      loadUserInfo();
    }
  }, [open, userIdToFetch]);

  const loadUserInfo = async () => {
    if (!userIdToFetch) return;
    
    setLoading(true);
    try {
      const user = await getUserById(userIdToFetch);
      setUserInfo(user);
    } catch (error) {
      console.error('Error loading user info:', error);
      setUserInfo(null);
    } finally {
      setLoading(false);
    }
  };

  const handleAction = async () => {
    if (!order) return;

    setActionLoading(true);
    try {
      const functionName = orderType === 'buyer' ? 'makePurchase' : 'completePurchase';
      const actionFunction = httpsCallable(firebaseFunctions, functionName);
      
      const result = await actionFunction({
        orderId: order.id
      });

      toast.success(result.data.message || 'Action completed successfully!');
      onOpenChange(false);
      
      if (onOrderAction) {
        onOrderAction();
      }
      
    } catch (error: any) {
      console.error('Action failed:', error);
      const errorMessage = error.message || 'Action failed. Please try again.';
      toast.error(errorMessage);
    } finally {
      setActionLoading(false);
    }
  };

  const getImageSrc = () => {
    if (!order?.collectionId) return '';
    // Use PNG by default to avoid TGS 404 errors
    return `/limited/${order.collectionId}/Original.png`;
  };

  const handleImageError = () => {
    setImageError(true);
  };

  const handleClose = () => {
    setUserInfo(null);
    setImageError(false);
    onOpenChange(false);
  };

  if (!order) return null;

  return (
    <Drawer.Root open={open} onOpenChange={onOpenChange}>
      <Drawer.Portal>
        <Drawer.Overlay className="fixed inset-0 bg-black/40 z-50" />
        <Drawer.Content className="bg-slate-800 flex flex-col rounded-t-[10px] h-fit mt-24 max-h-[80vh] fixed bottom-0 left-0 right-0 z-50 border-t border-slate-700">
          <div className="p-4 bg-slate-800 rounded-t-[10px] flex-1 overflow-y-auto">
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-gray-600 mb-8" />
            
            <div className="max-w-md mx-auto space-y-6">
              <Drawer.Title className="font-medium text-lg flex items-center gap-2 text-white">
                <Package className="w-5 h-5 text-ton-main" />
                Order Details
              </Drawer.Title>

              {/* Order Image */}
              <div className="flex justify-center">
                <div className="w-32 h-32 relative rounded-lg overflow-hidden bg-slate-700">
                  <Image
                    src={getImageSrc()}
                    alt={collection?.name || 'Order item'}
                    fill
                    className="object-cover"
                    onError={handleImageError}
                  />
                </div>
              </div>

              {/* Collection Info */}
              <div className="space-y-3">
                <div>
                  <h3 className="font-semibold text-lg text-white">{collection?.name || 'Unknown Collection'}</h3>
                  {collection?.description && (
                    <p className="text-gray-400 text-sm mt-1">{collection.description}</p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">Floor Price</span>
                    <p className="font-medium text-white">{collection?.floorPrice || 'N/A'} TON</p>
                  </div>
                  <div>
                    <span className="text-gray-400">Order Price</span>
                    <p className="font-medium text-ton-main">{order.amount} TON</p>
                  </div>
                </div>

                <div>
                  <span className="text-gray-400 text-sm">Status</span>
                  <div className="mt-1">
                    <Badge variant="secondary">{collection?.status}</Badge>
                  </div>
                </div>
              </div>

              {/* User Info */}
              <div className="border-t border-slate-600 pt-4">
                <div className="flex items-center gap-2 mb-3">
                  <User className="w-4 h-4 text-gray-400" />
                  <span className="text-sm font-medium text-white">
                    {orderType === 'seller' ? 'Buyer Information' : 'Seller Information'}
                  </span>
                </div>

                {loading ? (
                  <div className="flex items-center gap-2 text-gray-400">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span className="text-sm">Loading user info...</span>
                  </div>
                ) : userInfo ? (
                  <div className="bg-slate-700 border border-slate-600 rounded-lg p-3">
                    <p className="font-medium text-white">{userInfo.displayName || userInfo.email || 'Anonymous User'}</p>
                    {userInfo.email && userInfo.displayName && (
                      <p className="text-sm text-gray-400">{userInfo.email}</p>
                    )}
                  </div>
                ) : (
                  <p className="text-sm text-gray-400">User information not available</p>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4">
                <Button
                  variant="outline"
                  onClick={handleClose}
                  className="flex-1 border-slate-600 text-white hover:bg-slate-700"
                  disabled={actionLoading}
                >
                  Close
                </Button>
                <Button
                  onClick={handleAction}
                  disabled={actionLoading}
                  className="flex-1 bg-ton-main hover:bg-ton-main/90"
                >
                  {actionLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin mr-2" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <ShoppingCart className="w-4 h-4 mr-2" />
                      {orderType === 'buyer' ? 'Buy' : 'Fulfill'}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
}
