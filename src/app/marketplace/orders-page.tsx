"use client";

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useCallback, useEffect, useRef, useState } from 'react';

import { getOrdersForBuyers, getOrdersForSellers } from '@/api/order-api';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { OrderEntity } from '@/core.constants';
import { useRootContext } from '@/root-context';
import { Loader2, Plus } from 'lucide-react';
import { CreateOrderDrawer } from './create-order-drawer';
import { OrderCard } from './order-card';
import { OrderDetailsDrawer } from './order-details-drawer';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CollectionSelect } from '@/components/ui/collection-select';

interface OrdersPageProps {}

export default function OrdersPage({}: OrdersPageProps) {
  const { collections } = useRootContext();
  const [activeTab, setActiveTab] = useState<'sellers' | 'buyers'>('sellers');
  const [showCreateOrderDrawer, setShowCreateOrderDrawer] = useState(false);
  const [showOrderDetailsDrawer, setShowOrderDetailsDrawer] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<OrderEntity | null>(null);
  // Separate state for each tab to prevent duplicate keys and improve UX
  const [sellersOrders, setSellersOrders] = useState<OrderEntity[]>([]);
  const [buyersOrders, setBuyersOrders] = useState<OrderEntity[]>([]);
  const [sellersLoading, setSellersLoading] = useState(false);
  const [buyersLoading, setBuyersLoading] = useState(false);
  const [sellersLoadingMore, setSellersLoadingMore] = useState(false);
  const [buyersLoadingMore, setBuyersLoadingMore] = useState(false);
  const [sellersHasMore, setSellersHasMore] = useState(true);
  const [buyersHasMore, setBuyersHasMore] = useState(true);
  const [sellersLastDoc, setSellersLastDoc] = useState<any>(null);
  const [buyersLastDoc, setBuyersLastDoc] = useState<any>(null);

  // Filters
  const [minPrice, setMinPrice] = useState('');
  const [maxPrice, setMaxPrice] = useState('');
  const [selectedCollection, setSelectedCollection] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'price_asc' | 'price_desc' | 'date_asc' | 'date_desc'>('date_desc');

  // Ref for infinite scroll
  const loadMoreRef = useRef<HTMLDivElement>(null);

  const loadOrders = useCallback(async (reset = true) => {
    const isSellersTab = activeTab === 'sellers';

    if (reset) {
      if (isSellersTab) {
        setSellersLoading(true);
        setSellersOrders([]);
        setSellersLastDoc(null);
        setSellersHasMore(true);
      } else {
        setBuyersLoading(true);
        setBuyersOrders([]);
        setBuyersLastDoc(null);
        setBuyersHasMore(true);
      }
    } else {
      if (isSellersTab) {
        setSellersLoadingMore(true);
      } else {
        setBuyersLoadingMore(true);
      }
    }

    try {
      const currentLastDoc = isSellersTab ? sellersLastDoc : buyersLastDoc;
      const filters = {
        minPrice: minPrice ? parseFloat(minPrice) : undefined,
        maxPrice: maxPrice ? parseFloat(maxPrice) : undefined,
        collectionId: selectedCollection !== 'all' ? selectedCollection : undefined,
        sortBy,
        limit: 3,
        lastDoc: reset ? null : currentLastDoc,
      };

      let result;
      if (isSellersTab) {
        // For sellers tab: get orders where buyerId !== null and sellerId === null
        result = await getOrdersForSellers(filters);
      } else {
        // For buyers tab: get orders where sellerId !== null and buyerId === null
        result = await getOrdersForBuyers(filters);
      }

      if (isSellersTab) {
        if (reset) {
          console.log('🔄 Resetting sellers orders with', result.orders.length, 'orders');
          setSellersOrders(result.orders);
        } else {
          // Deduplicate orders to prevent duplicate keys
          setSellersOrders(prev => {
            const existingIds = new Set(prev.map(order => order.id));
            const newOrders = result.orders.filter(order => !existingIds.has(order.id));
            const duplicates = result.orders.filter(order => existingIds.has(order.id));

            if (duplicates.length > 0) {
              console.warn('🚨 Found duplicate orders for sellers:', duplicates.map(o => o.id));
            }

            console.log('➕ Adding', newOrders.length, 'new sellers orders, filtered out', duplicates.length, 'duplicates');
            return [...prev, ...newOrders];
          });
        }
        setSellersLastDoc(result.lastDoc);
        setSellersHasMore(result.hasMore);
      } else {
        if (reset) {
          console.log('🔄 Resetting buyers orders with', result.orders.length, 'orders');
          setBuyersOrders(result.orders);
        } else {
          // Deduplicate orders to prevent duplicate keys
          setBuyersOrders(prev => {
            const existingIds = new Set(prev.map(order => order.id));
            const newOrders = result.orders.filter(order => !existingIds.has(order.id));
            const duplicates = result.orders.filter(order => existingIds.has(order.id));

            if (duplicates.length > 0) {
              console.warn('🚨 Found duplicate orders for buyers:', duplicates.map(o => o.id));
            }

            console.log('➕ Adding', newOrders.length, 'new buyers orders, filtered out', duplicates.length, 'duplicates');
            return [...prev, ...newOrders];
          });
        }
        setBuyersLastDoc(result.lastDoc);
        setBuyersHasMore(result.hasMore);
      }
    } catch (error) {
      console.error('Error loading orders:', error);
    } finally {
      if (isSellersTab) {
        setSellersLoading(false);
        setSellersLoadingMore(false);
      } else {
        setBuyersLoading(false);
        setBuyersLoadingMore(false);
      }
    }
  }, [activeTab, minPrice, maxPrice, selectedCollection, sortBy, sellersLastDoc, buyersLastDoc]);

  const loadMore = useCallback(() => {
    loadOrders(false);
  }, [loadOrders]);

  useEffect(() => {
    loadOrders(true);
  }, [activeTab, minPrice, maxPrice, selectedCollection, sortBy]);

  // Intersection Observer for infinite scroll
  useEffect(() => {
    const isSellersTab = activeTab === 'sellers';
    const hasMore = isSellersTab ? sellersHasMore : buyersHasMore;
    const loading = isSellersTab ? sellersLoading : buyersLoading;
    const loadingMore = isSellersTab ? sellersLoadingMore : buyersLoadingMore;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !loading && !loadingMore) {
          loadMore();
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => {
      if (loadMoreRef.current) {
        observer.unobserve(loadMoreRef.current);
      }
    };
  }, [loadMore, activeTab, sellersHasMore, buyersHasMore, sellersLoading, buyersLoading, sellersLoadingMore, buyersLoadingMore]);

  const handleCreateOrder = () => {
    setShowCreateOrderDrawer(true);
  };

  const handleOrderCreated = () => {
    loadOrders(true); // Refresh orders after creating
  };

  const handleOrderClick = (order: OrderEntity) => {
    setSelectedOrder(order);
    setShowOrderDetailsDrawer(true);
  };

  const handleOrderAction = () => {
    loadOrders(true); // Refresh orders after action
  };



  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-white">Orders</h1>
        <div className="flex items-center gap-2">
          {/* <Button
            variant="outline"
            onClick={() => debugAllOrders()}
            className="text-white border-gray-600 hover:bg-gray-700"
          >
            Debug Orders
          </Button> */}
          <Button
            onClick={handleCreateOrder}
            className="flex items-center gap-2 bg-ton-main hover:bg-ton-main/90"
          >
            <Plus className="w-4 h-4" />
            Create Order
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'sellers' | 'buyers')}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="sellers">For Sellers</TabsTrigger>
          <TabsTrigger value="buyers">For Buyers</TabsTrigger>
        </TabsList>

        {/* Unified Filters - Single Row */}
        <div className="flex flex-wrap items-end gap-2 p-3 bg-gray-800/50 rounded-lg">
          <div className="flex-1 min-w-[100px] xss:min-w-[120px]">
            <Input
              type="number"
              placeholder="Min TON"
              value={minPrice}
              onChange={(e) => setMinPrice(e.target.value)}
              className="text-white text-sm h-9"
            />
          </div>
          <div className="flex-1 min-w-[100px] xss:min-w-[120px]">
            <Input
              type="number"
              placeholder="Max TON"
              value={maxPrice}
              onChange={(e) => setMaxPrice(e.target.value)}
              className="text-white text-sm h-9"
            />
          </div>
          <div className="flex-1 min-w-[120px] xss:min-w-[140px]">
            <CollectionSelect
              animated
              collections={collections}
              value={selectedCollection}
              onValueChange={setSelectedCollection}
              placeholder="All Collections"
              className="h-9 text-sm"
            />
          </div>
          <div className="flex-1 min-w-[120px] xss:min-w-[140px]">
            <Select value={sortBy} onValueChange={(value) => setSortBy(value as any)}>
              <SelectTrigger className="h-9 text-sm bg-transparent border-gray-600 text-white hover:bg-slate-700">
                <SelectValue placeholder="Sort by..." className="text-white" />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-gray-600">
                <SelectItem value="date_desc" className="text-white hover:bg-slate-700 focus:bg-slate-700">Newest First</SelectItem>
                <SelectItem value="date_asc" className="text-white hover:bg-slate-700 focus:bg-slate-700">Oldest First</SelectItem>
                <SelectItem value="price_desc" className="text-white hover:bg-slate-700 focus:bg-slate-700">Price: High to Low</SelectItem>
                <SelectItem value="price_asc" className="text-white hover:bg-slate-700 focus:bg-slate-700">Price: Low to High</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <TabsContent value="sellers" className="space-y-4">
          <div className="text-sm text-gray-400">
            Orders where buyers are looking for sellers (buyerId exists, sellerId needed)
          </div>
          {sellersLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-ton-main mx-auto"></div>
              <p className="text-gray-400 mt-2">Loading orders...</p>
            </div>
          ) : sellersOrders.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-400">No orders found for sellers</p>
            </div>
          ) : (
            <div className="grid grid-cols-2 xl:grid-cols-4 gap-2">
              {sellersOrders.map((order, index) => (
                <OrderCard
                   animated
                  key={`sellers-${order.id}-${index}`}
                  order={order}
                  collection={collections.find(c => c.id === order.collectionId)}
                  onClick={() => handleOrderClick(order)}
                />
              ))}
            </div>
          )}

          {/* Load more trigger for infinite scroll */}
          {sellersHasMore && (
            <div ref={activeTab === 'sellers' ? loadMoreRef : null} className="flex flex-col items-center gap-2 py-4">
              {sellersLoadingMore ? (
                <div className="flex items-center gap-2 text-gray-400">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Loading more orders...</span>
                </div>
              ) : (
                <Button
                  variant="outline"
                  onClick={() => loadMore()}
                  className="text-white border-gray-600 hover:bg-gray-700"
                >
                  Load More Orders
                </Button>
              )}
            </div>
          )}
        </TabsContent>

        <TabsContent value="buyers" className="space-y-4">
          <div className="text-sm text-gray-400">
            Orders where sellers are looking for buyers (sellerId exists, buyerId needed)
          </div>
          {buyersLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-ton-main mx-auto"></div>
              <p className="text-gray-400 mt-2">Loading orders...</p>
            </div>
          ) : buyersOrders.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-400">No orders found for buyers</p>
            </div>
          ) : (
            <div className="grid grid-cols-2 xl:grid-cols-4 gap-2">
              {buyersOrders.map((order, index) => (
                <OrderCard
                 animated
                  key={`buyers-${order.id}-${index}`}
                  order={order}
                  collection={collections.find(c => c.id === order.collectionId)}
                  onClick={() => handleOrderClick(order)}
                />
              ))}
            </div>
          )}

          {/* Load more trigger for infinite scroll */}
          {buyersHasMore && (
            <div ref={activeTab === 'buyers' ? loadMoreRef : null} className="flex flex-col items-center gap-2 py-4">
              {buyersLoadingMore ? (
                <div className="flex items-center gap-2 text-gray-400">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Loading more orders...</span>
                </div>
              ) : (
                <Button
                  variant="outline"
                  onClick={() => loadMore()}
                  className="text-white border-gray-600 hover:bg-gray-700"
                >
                  Load More Orders
                </Button>
              )}
            </div>
          )}
        </TabsContent>
      </Tabs>

      <CreateOrderDrawer
        open={showCreateOrderDrawer}
        onOpenChange={setShowCreateOrderDrawer}
        orderType={activeTab === 'sellers' ? 'buyer' : 'seller'}
        onOrderCreated={handleOrderCreated}
      />

      <OrderDetailsDrawer
        open={showOrderDetailsDrawer}
        onOpenChange={setShowOrderDetailsDrawer}
        order={selectedOrder}
        orderType={activeTab === 'sellers' ? 'seller' : 'buyer'}
        onOrderAction={handleOrderAction}
      />
    </div>
  );
}
