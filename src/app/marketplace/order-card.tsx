"use client";

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { OrderEntity, Collection } from '@/core.constants';
import Image from 'next/image';
import TgsViewer from '@/components/TgsViewer';

interface OrderCardProps {
  order: OrderEntity;
  collection: Collection | undefined;
  onClick: () => void;
  animated?: boolean;
}

export function OrderCard({ animated, order, collection, onClick }: OrderCardProps) {
  const [imageError, setImageError] = useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <Card 
      className="bg-slate-800 border-slate-700 hover:bg-slate-750 transition-colors cursor-pointer group"
      onClick={onClick}
    >
      <CardContent className="p-2">
        {/* Order Image */}
        <div className="aspect-square relative rounded-lg overflow-hidden bg-slate-700 mb-3">
          {animated ? (
            <TgsViewer
              tgsUrl={`/limited/${order.collectionId}/Original.tgs`}
              style={{ height: 'auto', width: 'auto', padding: '16px' }}
            />
          ) : (
            <Image
            src={`/limited/${order.collectionId}/Original.png`}
            alt={collection?.name || 'Order item'}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-200"
            onError={handleImageError}
          />)}
        </div>

        {/* Collection Name */}
        <h3 className="font-medium text-white text-sm mb-2 line-clamp-2 truncate">
          {collection?.name || 'Unknown Collection'}
        </h3>

        {/* Price and Status */}
        <div className="flex items-center justify-between">
          <div>
            <p className="text-ton-main text-xs font-semibold">
              {order.amount} TON
            </p>
          </div>
          <Badge variant="secondary" className="text-[8px] px-[6px] py-[2px]">
            {collection?.status}
          </Badge>
        </div>

        {/* Order Number */}
        <p className="text-gray-400 text-xs mt-2">
          Order #{order.number || (typeof order.id === 'string' ? order.id?.slice(-6) : 'N/A')}
        </p>
      </CardContent>
    </Card>
  );
}
