import dotenv from "dotenv";
import { Context } from "telegraf";
import { MESSAGES } from "../constants/messages";
import { getOrderByIdByBot, sendGiftToRelayer } from "../firebase-service";
import { clearUserSession, getUserSession } from "../services/session";

dotenv.config();

interface OrderData {
  id: string;
  collectionId: string;
  buyerId?: string;
  sellerId?: string;
  status: string;
  [key: string]: any;
}

const BOT_TOKEN = process.env.BOT_TOKEN;

export type TelegramBusinessMessageContext = {
  update_id: number;
  business_message: {
    business_connection_id: string;
    message_id: number;
    from: {
      id: number;
      is_bot: boolean;
      first_name: string;
      username: string;
      language_code: string;
      is_premium: boolean;
    };
    chat: {
      id: number;
      first_name: string;
      username: string;
      type: "private" | "group" | "supergroup" | "channel";
    };
    date: number;
    unique_gift: {
      gift: {
        owned_gift_id: string;
        gift: {
          id: string;
          sticker: any;
          star_count: number;
          total_count?: number;
          remaining_count?: number;
          upgrade_star_count?: number;
        };
        date: number;
        is_saved: boolean;
        is_name_hidden: boolean;
        is_unique?: boolean; // Unique gifts can be transferred
      }; // Replace with exact shape if known
      origin: string;
    };
  };
};

const getSentGiftId = (ctx: Context) => {
  const businessMessage = (ctx.update as any).business_message;
  const giftId = businessMessage?.unique_gift?.owned_gift_id;

  return giftId;
};

const getGiftCollectionId = (ctx: Context) => {
  const businessMessage = (ctx.update as any).business_message;
  const collectionId = businessMessage?.unique_gift?.gift?.gift?.id;

  return collectionId;
};

const getBusinessConnectionId = (ctx: Context) => {
  const businessMessage = (ctx.update as any).business_message;
  return businessMessage?.business_connection_id;
};

// Get order by ID using cloud function
const getOrderById = async (orderId: string): Promise<OrderData | null> => {
  try {
    const result = await getOrderByIdByBot(orderId);
    if (result.success && result.order) {
      return result.order as OrderData;
    }
    return null;
  } catch (error) {
    console.error("Error getting order:", error);
    return null;
  }
};

const transferGift = async (
  ctx: Context,
  businessConnectionId: string,
  chatId: number,
  owned_gift_id: string
) => {
  try {
    const sendGiftResponse = await fetch(
      `https://api.telegram.org/bot${BOT_TOKEN}/transferGift`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          business_connection_id: businessConnectionId,
          new_owner_chat_id: chatId,
          star_count: 25,
          owned_gift_id,
        }),
      }
    );

    const sendGiftResult = (await sendGiftResponse.json()) as {
      ok: boolean;
      description?: string;
      error_code?: number;
    };

    console.log("sendGiftResult", sendGiftResult);

    if (sendGiftResult.ok) {
      console.log(`Successfully sent gift back to user ${chatId}`);
      await ctx.telegram.sendMessage(
        chatId,
        MESSAGES.BUSINESS_CONNECTION.GIFT_TRANSFERRED_SUCCESS
      );
    } else {
      console.error("Failed to transfer gift:", sendGiftResult);
      await ctx.telegram.sendMessage(
        chatId,
        MESSAGES.BUSINESS_CONNECTION.GIFT_TRANSFER_GENERIC_ERROR
      );
    }
  } catch (error) {
    console.error("Error transferring gift:", error);
    await ctx.telegram.sendMessage(
      chatId,
      MESSAGES.BUSINESS_CONNECTION.GIFT_TRANSFER_GENERIC_ERROR
    );
  }
};

const getBusinessAccountGifts = async (
  businessConnectionId: string
): Promise<any[]> => {
  try {
    const response = await fetch(
      `https://api.telegram.org/bot${BOT_TOKEN}/getBusinessAccountGifts`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          business_connection_id: businessConnectionId,
          limit: 10,
        }),
      }
    );

    const result = (await response.json()) as {
      ok: boolean;
      result?: {
        gifts: any[];
      };
    };

    if (result.ok && result.result?.gifts) {
      console.log(
        "getBusinessAccountGifts result",
        JSON.stringify(result.result.gifts)
      );

      return result.result.gifts;
    }

    return [];
  } catch (error) {
    console.error("Error getting business account gifts:", error);
    return [];
  }
};

export const businessConnectionMiddleware = async (
  ctx: Context,
  next: () => Promise<void>
) => {
  try {
    const update = ctx.update as unknown as TelegramBusinessMessageContext;

    if (!update?.business_message) {
      await next();
      return;
    }

    const chat_id = update.business_message.chat.id;

    const userId = update.business_message.from?.id?.toString();
    if (!userId) {
      await next();
      return;
    }

    const session = getUserSession(userId);

    const pendingOrderId = session?.pendingOrderId;

    await getBusinessAccountGifts(getBusinessConnectionId(ctx));

    if (!pendingOrderId) {
      await ctx.telegram.sendMessage(chat_id, "No pending order found");

      await next();
      return;
    }

    const giftIdToTransfer = getSentGiftId(ctx);

    if (giftIdToTransfer) {
      const giftCollectionId = getGiftCollectionId(ctx);

      // Get order with firestore by pendingOrderId, check if collectionId matches
      const order = await getOrderById(pendingOrderId);

      if (!order) {
        await ctx.telegram.sendMessage(
          chat_id,
          MESSAGES.BUSINESS_CONNECTION.ORDER_NOT_FOUND
        );
        await next();
        return;
      }

      // TODO fix this
      if (order.collectionId !== giftCollectionId) {
        await ctx.telegram.sendMessage(
          chat_id,
          MESSAGES.BUSINESS_CONNECTION.INCORRECT_GIFT
        );
        await next();
        return;
      }

      // Gift is correct, process it
      await handleGiftToRelayer(ctx, pendingOrderId, giftIdToTransfer, chat_id);

      // Clear user session after successful processing
      clearUserSession(userId);
      await next();
      return;
    }

    // Logic for buyer to request the gift
    // If no gift to transfer, check if user has an order with status 'gift_sent_to_relayer'
    const existingOrder = await getOrderByIdByBot(pendingOrderId);

    if (!existingOrder) {
      await ctx.telegram.sendMessage(
        chat_id,
        MESSAGES.BUSINESS_CONNECTION.NO_ORDER_FOR_PROCESSING
      );
      await next();
      return;
    }

    const giftToTransferToBuyer = existingOrder.owned_gift_id;

    if (!giftToTransferToBuyer) {
      await ctx.telegram.sendMessage(
        chat_id,
        MESSAGES.BUSINESS_CONNECTION.NO_GIFT_TO_TRANSFER
      );
      await next();
      return;
    }

    const businessConnectionId = getBusinessConnectionId(ctx);

    if (businessConnectionId) {
      await transferGift(
        ctx,
        businessConnectionId,
        chat_id,
        giftToTransferToBuyer
      );
      clearUserSession(userId);
    } else {
      await ctx.telegram.sendMessage(
        chat_id,
        MESSAGES.BUSINESS_CONNECTION.GIFT_TRANSFER_MISSING_INFO
      );
    }

    await next();
  } catch (error) {
    console.error("Error handling update:", error);
    await next();
  }
};

export const handleGiftToRelayer = async (
  ctx: Context,
  orderId: string,
  owned_gift_id: string,
  chat_id: number
) => {
  try {
    await ctx.telegram.sendMessage(
      chat_id,
      MESSAGES.BUSINESS_CONNECTION.PROCESSING_GIFT
    );

    // Update order status to gift_sent_to_relayer
    const result = await sendGiftToRelayer(orderId, owned_gift_id);

    if (result.success) {
      await ctx.telegram.sendMessage(
        chat_id,
        MESSAGES.BUSINESS_CONNECTION.GIFT_SENT_SUCCESS
      );
    } else {
      await ctx.telegram.sendMessage(
        chat_id,
        MESSAGES.BUSINESS_CONNECTION.GIFT_PROCESSING_GENERIC_ERROR(orderId)
      );
    }
  } catch (error) {
    console.error("Error handling gift to relayer:", error);

    await ctx.telegram.sendMessage(
      chat_id,
      MESSAGES.BUSINESS_CONNECTION.GIFT_PROCESSING_GENERIC_ERROR(orderId)
    );
  }
};
